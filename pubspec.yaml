name: jeepers_app
description: Jeepers Flutter Application

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43W
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.3+2

environment:
  sdk: ">=3.0.5 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5
  firebase_crashlytics: ^3.4.9
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.8.0
  #  amplify_flutter: ^1.3.3
  #  amplify_analytics_pinpoint: ^1.3.2
  firebase_auth: ^4.16.0
  firebase_core: ^2.24.2
  #  google_maps_flutter: ^2.1.1
  # Firebase dependency ends
  get: ^4.6.5
  flutter_svg: ^2.0.7
  shared_preferences: ^2.1.2
  flutter_form_builder: 9.1.1
  google_fonts: ^5.1.0
  flutter_icons_helper: ^0.0.5
  chopper: ^6.1.4
  provider: ^6.1.2
  connectivity_plus: ^4.0.0
  url_launcher: ^6.1.11
  font_awesome_flutter: 10.5.0
  form_builder_validators: ^9.0.0
  app_tracking_transparency: ^2.0.4
  flutter_html: ^3.0.0-beta.2
  flutter_widget_from_html: ^0.14.11
  flutter_inappwebview: ^6.0.0
  device_preview: ^1.1.0
  country_icons: ^2.0.2
  lottie: ^2.4.0
  flutter_custom_clippers: ^2.1.0
  sliding_up_panel: ^2.0.0+1
  badges: ^3.1.1
  webview_flutter: ^4.2.2
  google_maps_flutter: ^2.3.1
  http: ^1.0.0
  infinite_scroll_pagination: ^3.2.0
  pinput: ^2.2.31
  uni_links: ^0.5.1

  #newly added for nature's cart
  otp_pin_field: ^1.2.2
  hidden_drawer_menu: ^3.0.1
  carousel_slider: ^4.2.1
  dotted_decoration: ^2.0.0
  flutter_slidable: ^3.0.0
  geolocator: ^9.0.2
  timelines: ^0.1.0
  cached_network_image: ^3.2.3
  google_sign_in: ^6.1.6
  sign_in_with_apple: ^5.0.0
  #  flutter_facebook_auth: ^6.0.0
  image_picker: ^1.0.1
  image_cropper: ^5.0.0
  #  flutter_stripe: ^9.3.0
  package_info_plus: ^4.1.0
  flutter_local_notifications: ^15.1.1
  google_places_flutter: ^2.0.6
  geocoding: ^2.1.0
  photo_view: ^0.14.0
  flutter_screenutil: ^5.9.0
  pixel_snap: ^0.1.2
  loading_animation_widget: ^1.2.0+4
  flutter_credit_card: ^3.0.7
  otp_autofill: ^3.0.0
  #  flutter_file_downloader: ^1.1.4
  flutter_downloader: ^1.11.4
  flutter_cached_pdfview: ^0.4.2
  #  al_downloader: ^1.7.9
  permission_handler: ^11.0.0
  add_to_cart_animation: ^2.0.3
  share_plus: ^7.2.1
  flutter_rating_bar: ^4.0.1
  shimmer: ^3.0.0
  form_builder_phone_field: ^2.0.1
  google_ml_vision: ^0.0.8
  sentry_flutter: ^8.8.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.1
  flutter_native_splash: ^2.3.1
  flutter_launcher_icons: ^0.13.1
  build_runner: ^2.4.5
  drift_dev: ^2.9.0
  chopper_generator: ^6.0.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/
    - assets/lottie_animation/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Impacted
      fonts:
        - asset: assets/fonts/impacted-2-0.ttf

  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/hangout_icon.png"
  min_sdk_android: 21

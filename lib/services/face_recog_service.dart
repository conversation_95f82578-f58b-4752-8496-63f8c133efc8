import 'dart:io';

import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

class ImageFaceRecognitionService {
  static Future<bool> checkFace(File? imageFile) async {
    List<Face> faces = [];
    if (imageFile != null) {
      final InputImage inputImage = InputImage.fromFile(imageFile);
      final FaceDetector faceDetector = FaceDetector(
        options: FaceDetectorOptions(
          enableContours: true,
          enableClassification: true,
        ),
      );

      try {
        faces = await faceDetector.processImage(inputImage);
      } catch (e) {
        print('Error detecting faces: $e');
      } finally {
        faceDetector.close();
      }
    }

    return faces.length == 1;
  }
}

PODS:
  - app_tracking_transparency (0.0.1):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Firebase/Analytics (10.18.0):
    - Firebase/Core
  - Firebase/Auth (10.18.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.18.0)
  - Firebase/Core (10.18.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.18.0)
  - Firebase/CoreOnly (10.18.0):
    - FirebaseCore (= 10.18.0)
  - Firebase/Crashlytics (10.18.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.18.0)
  - Firebase/Messaging (10.18.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.18.0)
  - firebase_analytics (10.8.0):
    - Firebase/Analytics (= 10.18.0)
    - firebase_core
    - Flutter
  - firebase_auth (4.16.0):
    - Firebase/Auth (= 10.18.0)
    - firebase_core
    - Flutter
  - firebase_core (2.24.2):
    - Firebase/CoreOnly (= 10.18.0)
    - Flutter
  - firebase_crashlytics (3.4.9):
    - Firebase/Crashlytics (= 10.18.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.7.10):
    - Firebase/Messaging (= 10.18.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (10.18.0):
    - FirebaseAnalytics/AdIdSupport (= 10.18.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.18.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.18.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.18.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.18.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 9.0)
  - google_mlkit_commons (0.7.1):
    - Flutter
    - MLKitVision
  - google_mlkit_face_detection (0.10.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/FaceDetection (~> 5.0.0)
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 7.0)
  - GoogleAppMeasurement (10.18.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.18.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.18.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.18.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.18.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleMLKit/FaceDetection (5.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 4.0.0)
  - GoogleMLKit/MLKitCore (5.0.0):
    - MLKitCommon (~> 10.0.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleToolboxForMac/DebugUtils (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - GoogleToolboxForMac/Defines (2.3.2)
  - GoogleToolboxForMac/Logger (2.3.2):
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSData+zlib (2.3.2)":
    - GoogleToolboxForMac/Defines (= 2.3.2)
  - "GoogleToolboxForMac/NSDictionary+URLArguments (2.3.2)":
    - GoogleToolboxForMac/DebugUtils (= 2.3.2)
    - GoogleToolboxForMac/Defines (= 2.3.2)
    - "GoogleToolboxForMac/NSString+URLArguments (= 2.3.2)"
  - "GoogleToolboxForMac/NSString+URLArguments (2.3.2)"
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta5)
  - MLKitCommon (10.0.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - "GoogleToolboxForMac/NSDictionary+URLArguments (~> 2.1)"
    - GoogleUtilities/UserDefaults (~> 7.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
  - MLKitFaceDetection (4.0.0):
    - MLKitCommon (~> 10.0)
    - MLKitVision (~> 6.0)
  - MLKitVision (6.0.0):
    - GoogleToolboxForMac/Logger (~> 2.1)
    - "GoogleToolboxForMac/NSData+zlib (~> 2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 10.0)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - OrderedSet (5.0.0)
  - otp_autofill (0.0.1):
    - Flutter
  - "otp_pin_field (1.2.0+2)":
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - phone_number (2.0.1):
    - Flutter
    - PhoneNumberKit/PhoneNumberKitCore (= 3.6.6)
  - PhoneNumberKit/PhoneNumberKitCore (3.6.6)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - ReachabilitySwift (5.2.4)
  - RecaptchaInterop (100.0.0)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - smart_auth (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - TOCropViewController (2.6.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_face_detection (from `.symlinks/plugins/google_mlkit_face_detection/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - otp_autofill (from `.symlinks/plugins/otp_autofill/ios`)
  - otp_pin_field (from `.symlinks/plugins/otp_pin_field/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - phone_number (from `.symlinks/plugins/phone_number/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - smart_auth (from `.symlinks/plugins/smart_auth/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSessions
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleSignIn
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMAppAuth
    - GTMSessionFetcher
    - MLImage
    - MLKitCommon
    - MLKitFaceDetection
    - MLKitVision
    - nanopb
    - OrderedSet
    - PhoneNumberKit
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - RecaptchaInterop
    - Sentry
    - TOCropViewController

EXTERNAL SOURCES:
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_face_detection:
    :path: ".symlinks/plugins/google_mlkit_face_detection/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  otp_autofill:
    :path: ".symlinks/plugins/otp_autofill/ios"
  otp_pin_field:
    :path: ".symlinks/plugins/otp_pin_field/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  phone_number:
    :path: ".symlinks/plugins/phone_number/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  smart_auth:
    :path: ".symlinks/plugins/smart_auth/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  app_tracking_transparency: 5b1745ef9ade815f7455cb6a0848349589afb7c5
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  audio_session: 088d2483ebd1dc43f51d253d4a1c517d9a2e7207
  connectivity_plus: 07c49e96d7fc92bc9920617b83238c4d178b446a
  Firebase: 414ad272f8d02dfbf12662a9d43f4bba9bec2a06
  firebase_analytics: b3d6dd14c61549a29abb10e9843446a4d8bb53eb
  firebase_auth: 8e9ec02991ca4659111cc671c84d0c010b6bfb26
  firebase_core: 0af4a2b24f62071f9bf283691c0ee41556dcb3f5
  firebase_crashlytics: 4b91b8ad60ee7c168fe88979f84c9573a729de7a
  firebase_messaging: 90e8a6db84b6e1e876cebce4f30f01dc495e7014
  FirebaseAnalytics: 4d310b35c48eaa4a058ddc04bdca6bdb5dc0fe80
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: 12314b438fa76048540c8fb86d6cfc9e08595176
  FirebaseCore: 2322423314d92f946219c8791674d2f3345b598f
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 86d5bce01f42fa1db265f87ff1d591f04db610ec
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 9bc34a98d2e0237e1b121915120d4d48ddcf301e
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_downloader: b7301ae057deadd4b1650dc7c05375f10ff12c39
  flutter_inappwebview_ios: 97215cf7d4677db55df76782dbd2930c5e1c1ea0
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  flutter_native_splash: 52501b97d1c0a5f898d687f1646226c1f93c56ef
  flutter_pdfview: 25f53dd6097661e6395b17de506e6060585946bd
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  geocoding_ios: a389ea40f6f548de6e63006a2e31bf66ff80769a
  geolocator_apple: 9157311f654584b9bb72686c55fc02a97b73f461
  google_maps_flutter_ios: d1318b4ff711612cab16862d7a87e31a7403d458
  google_mlkit_commons: 96aaca445520311b84a2da013dedf3427fe4cc69
  google_mlkit_face_detection: c27eb0204f0ceeb439219516274a429509104f6f
  google_sign_in_ios: 8115e3fbe097e6509beb819ed602d47369d9011f
  GoogleAppMeasurement: 70ce9aa438cff1cfb31ea3e660bcc67734cb716e
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleMLKit: 90ba06e028795a50261f29500d238d6061538711
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleToolboxForMac: 8bef7c7c5cf7291c687cf5354f39f9db6399ad34
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_cropper: a3291c624a953049bc6a02e1f8c8ceb162a24b25
  image_picker_ios: 99dfe1854b4fa34d0364e74a78448a0151025425
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitCommon: afcd11b6c0735066a0dde8b4bf2331f6197cbca2
  MLKitFaceDetection: 96effb0e6407e299fc286034f45d55267a212c26
  MLKitVision: 90922bca854014a856f8b649d1f1f04f63fd9c79
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  otp_autofill: 93bcc21475705d35445927cb9aef24e818bbf083
  otp_pin_field: 0f0935fafc56252e6a48a5a045ac689b6bad3857
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  phone_number: 567a167933138df1080eb0d8ecb0ea44f1dee1ed
  PhoneNumberKit: 5b1be7ee4955dfeeb855f51eecdd829ab24b3484
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 2df8b0aab7e4aba81261c230cbea31c82a62dd1b
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  smart_auth: 4bedbc118723912d0e45a07e8ab34039c19e04f2
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: bbd758c6e7f9fd7b5b1d4cde34d2b95fcce5e812
  video_player_avfoundation: 81e49bb3d9fb63dccf9fa0f6d877dc3ddbeac126
  wakelock_plus: 8b09852c8876491e4b6d179e17dfe2a0b5f60d47
  webview_flutter_wkwebview: 4f3e50f7273d31e5500066ed267e3ae4309c5ae4

PODFILE CHECKSUM: 84943c6f4e18a91c88c1fa6cc2ca317e6f982e9c

COCOAPODS: 1.16.2
